- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  lbPolicy: CLUSTER_PROVIDED
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.override_host
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.override_host.v3.OverrideHost
          fallbackPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.round_robin
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.round_robin.v3.RoundRobin
          overrideHostSources:
          - header: target-pod
  name: first-route-dest
  perConnectionBufferLimitBytes: 32768
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  lbPolicy: CLUSTER_PROVIDED
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.override_host
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.override_host.v3.OverrideHost
          fallbackPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.round_robin
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.round_robin.v3.RoundRobin
          overrideHostSources:
          - metadata:
              key: envoy.lb
              path:
              - key: target-pod
  name: second-route-dest
  perConnectionBufferLimitBytes: 32768
- circuitBreakers:
    thresholds:
    - maxRetries: 1024
  commonLbConfig: {}
  connectTimeout: 10s
  dnsLookupFamily: V4_PREFERRED
  lbPolicy: CLUSTER_PROVIDED
  loadBalancingPolicy:
    policies:
    - typedExtensionConfig:
        name: envoy.load_balancing_policies.override_host
        typedConfig:
          '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.override_host.v3.OverrideHost
          fallbackPolicy:
            policies:
            - typedExtensionConfig:
                name: envoy.load_balancing_policies.round_robin
                typedConfig:
                  '@type': type.googleapis.com/envoy.extensions.load_balancing_policies.round_robin.v3.RoundRobin
          overrideHostSources:
          - header: target-pod
          - header: route-strategy
          - metadata:
              key: envoy.lb
              path:
              - key: override-host
  name: third-route-dest
  perConnectionBufferLimitBytes: 32768
